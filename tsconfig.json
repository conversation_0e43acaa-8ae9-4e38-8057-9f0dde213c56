{"extends": "astro/tsconfigs/strict", "compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/layouts/*": ["./src/layouts/*"], "@/utils/*": ["./src/utils/*"], "@/data/*": ["./src/data/*"], "@/backend": ["./backend/index.ts"], "@/backend/*": ["./backend/*"]}}, "include": ["src/**/*", "astro.config.mjs"], "exclude": ["node_modules", "dist"]}