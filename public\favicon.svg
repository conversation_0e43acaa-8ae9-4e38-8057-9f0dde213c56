<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" width="128" height="128">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="58" fill="url(#grad1)" stroke="#1E40AF" stroke-width="2"/>
  
  <!-- Brain-like pattern -->
  <path d="M35 45 Q50 35 65 45 Q80 35 95 45 Q90 55 75 65 Q65 75 55 65 Q40 55 35 45 Z" 
        fill="#DBEAFE" opacity="0.8"/>
  
  <!-- IQ Text -->
  <text x="64" y="52" font-family="Arial, sans-serif" font-size="18" font-weight="bold" 
        text-anchor="middle" fill="white">IQ</text>
  
  <!-- Connecting dots -->
  <circle cx="45" cy="75" r="3" fill="#60A5FA"/>
  <circle cx="64" cy="82" r="3" fill="#60A5FA"/>
  <circle cx="83" cy="75" r="3" fill="#60A5FA"/>
  
  <!-- Connection lines -->
  <line x1="45" y1="75" x2="64" y2="82" stroke="#60A5FA" stroke-width="2" opacity="0.7"/>
  <line x1="64" y1="82" x2="83" y2="75" stroke="#60A5FA" stroke-width="2" opacity="0.7"/>
  
  <!-- Small accent circle -->
  <circle cx="64" cy="95" r="6" fill="#F59E0B" opacity="0.9"/>
  <text x="64" y="100" font-family="Arial, sans-serif" font-size="8" font-weight="bold" 
        text-anchor="middle" fill="white">✓</text>
</svg>