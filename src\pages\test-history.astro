---
import BaseLayout from '../layouts/BaseLayout.astro';
import SEO from '../components/seo/SEO.astro';
import TestHistoryComponent from '../components/profile/TestHistoryComponent.tsx';

// Server-side data loading for instant display (giống như profile)
let initialTestHistory = {
  testHistory: [],
  isAuthenticated: false,
  totalTests: 0,
  averageScore: 0,
  bestScore: 0,
  loading: true
};

// Load data from server-side for instant display
try {
  // Set default data that will be instantly available
  initialTestHistory = {
    testHistory: [], // Will be loaded client-side from localStorage/backend
    isAuthenticated: false,
    totalTests: 0,
    averageScore: 0,
    bestScore: 0,
    loading: true
  };
  
} catch (error) {
  console.warn('Server-side test history loading failed:', error);
}
---

<BaseLayout>
  <SEO 
    title="Lịch sử Test IQ - Theo dõi tiến bộ trí tuệ"
    description="Xem lại tất cả các bài test IQ đã làm, theo dõi điểm số và sự tiến bộ qua thời gian. Phân tích chi tiết kết quả và xu hướng phát triển trí tuệ."
    canonical={Astro.url.href}
    slot="head"
  />
  
  <TestHistoryComponent client:load initialData={initialTestHistory} />
</BaseLayout> 