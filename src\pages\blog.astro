---
import BaseLayout from '../layouts/BaseLayout.astro';

// Demo blog data
const blogPosts = [
  {
    id: 1,
    title: '<PERSON><PERSON><PERSON> tăng IQ hiệu quả: 7 phương pháp khoa học',
    excerpt: '<PERSON>h<PERSON><PERSON> phá những phương pháp được chứng minh khoa học để cải thiện trí thông minh và nâng cao khả năng nhận thức của bạn.',
    content: '<PERSON>hiên cứu cho thấy IQ có thể được cải thiện thông qua các bài tập não bộ, thi<PERSON>n định, và học tập liên tục...',
    author: 'Dr<PERSON>',
    authorAvatar: 'NM',
    date: '2024-01-15',
    readTime: '5 phút đọc',
    category: '<PERSON><PERSON><PERSON> học',
    tags: ['IQ', '<PERSON><PERSON> bộ', '<PERSON><PERSON><PERSON> tập'],
    image: '/api/placeholder/400/240',
    featured: true
  },
  {
    id: 2,
    title: '<PERSON><PERSON><PERSON> về các loại trí thông minh: Từ IQ đến EQ',
    excerpt: 'Tìm hiểu về sự khác biệt giữa IQ, EQ, và các dạng trí thông minh khác để phát triển toàn diện bản thân.',
    content: 'Trí thông minh không chỉ có một dạng. Howard Gardner đã xác định 8 loại trí thông minh khác nhau...',
    author: 'ThS. Lê Hồng Anh',
    authorAvatar: 'LA',
    date: '2024-01-12',
    readTime: '7 phút đọc',
    category: 'Tâm lý học',
    tags: ['IQ', 'EQ', 'Trí thông minh'],
    image: '/api/placeholder/400/240',
    featured: false
  },
  {
    id: 3,
    title: 'Luyện tập trí nhớ: Kỹ thuật Palace Method',
    excerpt: 'Học cách sử dụng kỹ thuật cung điện trí nhớ để ghi nhớ thông tin một cách hiệu quả và lâu dài.',
    content: 'Palace Method là một trong những kỹ thuật ghi nhớ mạnh mẽ nhất, được sử dụng từ thời Hy Lạp cổ đại...',
    author: 'Gs. Trần Văn Bình',
    authorAvatar: 'TB',
    date: '2024-01-10',
    readTime: '6 phút đọc',
    category: 'Kỹ thuật',
    tags: ['Trí nhớ', 'Học tập', 'Kỹ thuật'],
    image: '/api/placeholder/400/240',
    featured: false
  },
  {
    id: 4,
    title: 'Ảnh hưởng của giấc ngủ đến khả năng nhận thức',
    excerpt: 'Tìm hiểu tại sao giấc ngủ chất lượng là yếu tố quan trọng để duy trì và cải thiện hiệu suất não bộ.',
    content: 'Nghiên cứu gần đây cho thấy giấc ngủ đóng vai trò quan trọng trong việc củng cố trí nhớ và tăng cường khả năng học tập...',
    author: 'Dr. Phạm Thu Hà',
    authorAvatar: 'PH',
    date: '2024-01-08',
    readTime: '4 phút đọc',
    category: 'Sức khỏe',
    tags: ['Giấc ngủ', 'Não bộ', 'Sức khỏe'],
    image: '/api/placeholder/400/240',
    featured: false
  },
  {
    id: 5,
    title: 'Dinh dưỡng cho não: Thực phẩm tăng cường trí tuệ',
    excerpt: 'Khám phá những thực phẩm giúp nuôi dưỡng não bộ và cải thiện khả năng tập trung, ghi nhớ.',
    content: 'Não bộ tiêu thụ khoảng 20% năng lượng cơ thể. Việc cung cấp dinh dưỡng phù hợp có thể cải thiện đáng kể hiệu suất nhận thức...',
    author: 'ThS. Võ Minh Tâm',
    authorAvatar: 'VT',
    date: '2024-01-05',
    readTime: '5 phút đọc',
    category: 'Dinh dưỡng',
    tags: ['Dinh dưỡng', 'Não bộ', 'Thực phẩm'],
    image: '/api/placeholder/400/240',
    featured: false
  },
  {
    id: 6,
    title: 'Stress và trí thông minh: Mối quan hệ phức tạp',
    excerpt: 'Tìm hiểu cách stress ảnh hưởng đến khả năng nhận thức và các phương pháp quản lý stress hiệu quả.',
    content: 'Stress vừa có thể là động lực vừa có thể là rào cản đối với hiệu suất nhận thức. Hiểu rõ mối quan hệ này giúp tối ưu hóa khả năng học tập...',
    author: 'Dr. Hoàng Văn Nam',
    authorAvatar: 'HN',
    date: '2024-01-03',
    readTime: '6 phút đọc',
    category: 'Tâm lý học',
    tags: ['Stress', 'Tâm lý', 'Nhận thức'],
    image: '/api/placeholder/400/240',
    featured: false
  },
  {
    id: 7,
    title: 'Công nghệ và não bộ: Tác động của kỷ nguyên số',
    excerpt: 'Phân tích cách công nghệ hiện đại ảnh hưởng đến cách chúng ta suy nghĩ và xử lý thông tin.',
    content: 'Kỷ nguyên số đã thay đổi cách não bộ hoạt động. Việc hiểu những thay đổi này giúp chúng ta sử dụng công nghệ một cách có lợi...',
    author: 'ThS. Đặng Thị Mai',
    authorAvatar: 'DM',
    date: '2024-01-01',
    readTime: '8 phút đọc',
    category: 'Công nghệ',
    tags: ['Công nghệ', 'Não bộ', 'Kỷ nguyên số'],
    image: '/api/placeholder/400/240',
    featured: false
  },
  {
    id: 8,
    title: 'Thiền định và trí tuệ: Khoa học đằng sau sự tĩnh lặng',
    excerpt: 'Khám phá những lợi ích được chứng minh khoa học của thiền định đối với khả năng nhận thức và trí thông minh.',
    content: 'Các nghiên cứu neuroplasticity cho thấy thiền định có thể thay đổi cấu trúc não bộ một cách tích cực...',
    author: 'Gs. Lý Thanh Sơn',
    authorAvatar: 'LS',
    date: '2023-12-28',
    readTime: '7 phút đọc',
    category: 'Tâm linh',
    tags: ['Thiền định', 'Tâm linh', 'Khoa học'],
    image: '/api/placeholder/400/240',
    featured: false
  },
  {
    id: 9,
    title: 'Trò chơi trí tuệ: Liệu có thực sự hiệu quả?',
    excerpt: 'Đánh giá khách quan về hiệu quả của các trò chơi rèn luyện trí tuệ và ứng dụng di động.',
    content: 'Ngành công nghiệp trò chơi trí tuệ trị giá hàng tỷ đô la. Nhưng liệu chúng có thực sự giúp cải thiện IQ?...',
    author: 'Dr. Ngô Quang Minh',
    authorAvatar: 'NQ',
    date: '2023-12-25',
    readTime: '5 phút đọc',
    category: 'Giải trí',
    tags: ['Trò chơi', 'Trí tuệ', 'Ứng dụng'],
    image: '/api/placeholder/400/240',
    featured: false
  },
  {
    id: 10,
    title: 'Tương lai của đo lường trí thông minh',
    excerpt: 'Nhìn về tương lai: Làm thế nào AI và công nghệ mới sẽ thay đổi cách chúng ta đánh giá trí thông minh.',
    content: 'Với sự phát triển của AI và machine learning, cách chúng ta hiểu và đo lường trí thông minh đang thay đổi...',
    author: 'ThS. Bùi Văn Hùng',
    authorAvatar: 'BH',
    date: '2023-12-22',
    readTime: '9 phút đọc',
    category: 'Tương lai',
    tags: ['AI', 'Tương lai', 'Đo lường'],
    image: '/api/placeholder/400/240',
    featured: false
  }
];

// Get unique categories for filter
const categories = [...new Set(blogPosts.map(post => post.category))];

// Helper function to get category color
const getCategoryColor = (category: string) => {
  const colors = {
    'Khoa học': 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300',
    'Tâm lý học': 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300',
    'Kỹ thuật': 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300',
    'Sức khỏe': 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300',
    'Dinh dưỡng': 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300',
    'Công nghệ': 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300',
    'Tâm linh': 'bg-pink-100 text-pink-700 dark:bg-pink-900/30 dark:text-pink-300',
    'Giải trí': 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300',
    'Tương lai': 'bg-cyan-100 text-cyan-700 dark:bg-cyan-900/30 dark:text-cyan-300'
  };
  return colors[category] || 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';
};

// Format date
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
---

<BaseLayout
  title="Blog - Kiến thức về IQ và Trí thông minh"
  description="Khám phá những bài viết chuyên sâu về IQ, trí thông minh, và phát triển nhận thức từ các chuyên gia hàng đầu."
>
  <!-- Hero Section -->
  <section class="relative bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-gray-900 dark:to-blue-950 pt-24 pb-16">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <!-- Badge -->
      <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 text-blue-800 dark:text-blue-300 text-sm font-medium mb-8">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        Blog kiến thức
      </div>
      
      <!-- Main heading -->
      <h1 class="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
        Blog IQ & Trí tuệ
      </h1>
      
      <!-- Subtitle -->
      <p class="text-xl md:text-2xl text-gray-600 dark:text-gray-300 font-medium mb-4">
        Khám phá kiến thức chuyên sâu về trí thông minh
      </p>
      
      <p class="text-lg text-gray-500 dark:text-gray-400 max-w-2xl mx-auto">
        Những bài viết được biên soạn bởi các chuyên gia hàng đầu về tâm lý học, khoa học nhận thức và phát triển trí tuệ
      </p>
    </div>
  </section>

  <!-- Categories Filter -->
  <section class="py-8 bg-white dark:bg-gray-800 border-b border-gray-100 dark:border-gray-700">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-wrap items-center justify-center gap-3">
        <button class="px-4 py-2 bg-blue-500 text-white dark:bg-blue-600 dark:text-white rounded-xl text-sm font-medium hover:bg-blue-600 dark:hover:bg-blue-700">
          Tất cả
        </button>
        {categories.map(category => (
          <button class="px-4 py-2 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-xl text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-600">
            {category}
          </button>
        ))}
      </div>
    </div>
  </section>

  <!-- Featured Post -->
  {blogPosts.filter(post => post.featured).map(post => (
    <section class="py-16 bg-white dark:bg-gray-800">
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl overflow-hidden border border-blue-100 dark:border-blue-900/50">
          <div class="grid md:grid-cols-2 gap-0">
            <!-- Content -->
            <div class="p-8 md:p-12 flex flex-col justify-center">
              <div class="mb-4">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 mb-4">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                  Nổi bật
                </span>
                <span class={`inline-flex px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(post.category)}`}>
                  {post.category}
                </span>
              </div>
              
              <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                {post.title}
              </h2>
              
              <p class="text-gray-600 dark:text-gray-300 text-lg mb-6 leading-relaxed">
                {post.excerpt}
              </p>
              
              <!-- Author & Meta -->
              <div class="flex items-center mb-6">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  {post.authorAvatar}
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{post.author}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    {formatDate(post.date)} · {post.readTime}
                  </p>
                </div>
              </div>
              
              <div>
                <a href={`/blog/${post.id}`} class="inline-flex items-center px-5 py-3 rounded-xl bg-blue-600 text-white text-sm font-semibold hover:bg-blue-700">
                  Đọc tiếp
                  <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </a>
              </div>
            </div>
            
            <!-- Image -->
            <div class="relative">
              <div class="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-500/20 opacity-60 md:opacity-100 dark:from-blue-500/30 dark:to-purple-600/30"></div>
              <img 
                src={post.image || 'https://via.placeholder.com/800x600'} 
                alt={post.title}
                class="w-full h-full object-cover md:h-full md:min-h-[400px]"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  ))}

  <!-- Posts Grid -->
  <section class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-12 text-center">Bài viết gần đây</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {blogPosts.filter(post => !post.featured).map(post => (
          <a href={`/blog/${post.id}`} class="group bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm hover:shadow-md border border-gray-100 dark:border-gray-700 flex flex-col">
            <!-- Post Image -->
            <div class="relative h-48 overflow-hidden">
              <div class="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-purple-500/10 group-hover:opacity-75 dark:from-blue-500/20 dark:to-purple-600/20"></div>
              <img 
                src={post.image || 'https://via.placeholder.com/400x240'} 
                alt={post.title} 
                class="w-full h-full object-cover"
              />
              
              <!-- Category Tag -->
              <span class={`absolute bottom-3 left-3 px-2 py-1 rounded-lg text-xs font-medium ${getCategoryColor(post.category)}`}>
                {post.category}
              </span>
            </div>
            
            <!-- Content -->
            <div class="p-5 flex-1 flex flex-col">
              <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                {post.title}
              </h3>
              
              <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 flex-1">
                {post.excerpt}
              </p>
              
              <!-- Footer -->
              <div class="flex items-center justify-between mt-auto pt-4 border-t border-gray-100 dark:border-gray-700">
                <div class="flex items-center">
                  <div class="w-7 h-7 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-xs">
                    {post.authorAvatar}
                  </div>
                  <span class="ml-2 text-xs text-gray-600 dark:text-gray-400">{post.author}</span>
                </div>
                
                <span class="text-xs text-gray-500 dark:text-gray-400">{formatDate(post.date).split(" ")[0]}</span>
              </div>
            </div>
          </a>
        ))}
      </div>
      
      <!-- Load More Button -->
      <div class="mt-16 text-center">
        <button class="px-6 py-3 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 font-medium inline-flex items-center">
          Xem thêm bài viết
          <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>
    </div>
  </section>

  <!-- Newsletter -->
  <section class="py-16 bg-white dark:bg-gray-800">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl overflow-hidden border border-blue-100 dark:border-blue-900/50 p-8 md:p-12">
        <div class="text-center mb-8">
          <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Nhận thông tin mới nhất
          </h2>
          <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Đăng ký để nhận thông báo về những bài viết mới nhất và các bài test IQ mới
          </p>
        </div>
        
        <form class="max-w-lg mx-auto">
          <div class="flex flex-col sm:flex-row gap-3">
            <input 
              type="email" 
              placeholder="Email của bạn" 
              class="flex-1 px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              required
            />
            <button 
              type="submit" 
              class="px-6 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
            >
              Đăng ký
            </button>
          </div>
          <p class="mt-3 text-xs text-center text-gray-500 dark:text-gray-400">
            Chúng tôi tôn trọng quyền riêng tư của bạn. Hủy đăng ký bất cứ lúc nào.
          </p>
        </form>
      </div>
    </div>
  </section>

  <!-- Blog Tags -->
  <section class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
        Chủ đề phổ biến
      </h2>
      
      <div class="flex flex-wrap justify-center gap-3">
        {[...new Set(blogPosts.flatMap(post => post.tags))].map(tag => (
          <a href={`/blog/tag/${tag}`} class="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 text-sm hover:bg-gray-50 dark:hover:bg-gray-700">
            #{tag}
          </a>
        ))}
      </div>
    </div>
  </section>
</BaseLayout>

<style>
  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Smooth animations */
  .fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>

<script>
  // Add scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry, index) => {
      if (entry.isIntersecting) {
        setTimeout(() => {
          entry.target.classList.add('fade-in-up');
        }, index * 100);
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);
  
  // Observe articles
  document.querySelectorAll('article').forEach(el => {
    observer.observe(el);
  });

  // Category filter functionality
  const categoryButtons = document.querySelectorAll('button');
  const articles = document.querySelectorAll('article');
  
  categoryButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Update active button
      categoryButtons.forEach(btn => {
        btn.classList.remove('bg-blue-500', 'text-white', 'dark:bg-blue-600', 'dark:text-white');
        btn.classList.add('bg-gray-100', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
      });
      
      button.classList.remove('bg-gray-100', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
      button.classList.add('bg-blue-500', 'text-white', 'dark:bg-blue-600', 'dark:text-white');
      
      // Filter logic would go here when connected to real data
      console.log('Filter by:', button.textContent);
    });
  });

  // Newsletter form
  const newsletterForm = document.querySelector('form');
  newsletterForm?.addEventListener('submit', (e) => {
    e.preventDefault();
    const email = e.target.querySelector('input[type="email"]').value;
    
    // Show success message
    const button = e.target.querySelector('button');
    const originalText = button.textContent;
    
    button.textContent = 'Đã đăng ký!';
    button.disabled = true;
    button.classList.add('bg-green-500', 'hover:bg-green-600', 'dark:bg-green-600', 'dark:hover:bg-green-700');
    button.classList.remove('bg-gradient-to-r', 'from-blue-500', 'to-indigo-600', 'bg-blue-600', 'hover:bg-blue-700', 'dark:bg-blue-700', 'dark:hover:bg-blue-800');
    
    // Reset after 3 seconds
    setTimeout(() => {
      button.textContent = originalText;
      button.disabled = false;
      button.classList.remove('bg-green-500', 'hover:bg-green-600', 'dark:bg-green-600', 'dark:hover:bg-green-700');
      button.classList.add('bg-blue-600', 'hover:bg-blue-700', 'dark:bg-blue-700', 'dark:hover:bg-blue-800');
      e.target.reset();
    }, 3000);
  });
</script>