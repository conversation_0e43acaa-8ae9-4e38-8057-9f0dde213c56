---
import BaseLayout from '../../layouts/BaseLayout.astro';
import IQTestWrapper from '../../components/tests/types/iq/IQTestWrapper.tsx';
import { testConfig, iqQuestions } from '../../data/iqQuestions';

// Simple locale function
const getLocale = (lang: string) => ({
  meta: {
    site_name: 'IQ Test Pro'
  }
});

const locale = getLocale('vi');
---

<BaseLayout
  title={`${testConfig.title} - ${locale.meta.site_name}`}
  description={testConfig.description}
>
  <!-- <PERSON><PERSON><PERSON> hiển thị intro ban đầu -->
  <div id="intro-section">
    <div class="max-w-4xl mx-auto text-center py-20 dark:bg-gray-900">
      <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-xl p-12 border border-gray-100 dark:border-gray-700">
        <div class="mb-8">
          <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4 font-display">
            Test IQ <PERSON>
          </h1>
          <p class="text-xl text-gray-600 dark:text-gray-300 mb-6">
            Đánh giá trí tuệ với {iqQuestions.length} câu hỏi trong {Math.floor(testConfig.timeLimit / 60)} phút
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-blue-50 dark:bg-blue-900/30 p-6 rounded-2xl">
              <div class="text-3xl mb-2">🧠</div>
              <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Tư duy logic</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">Kiểm tra khả năng suy luận và phân tích</p>
            </div>
            <div class="bg-green-50 dark:bg-green-900/30 p-6 rounded-2xl">
              <div class="text-3xl mb-2">⏱️</div>
              <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Tốc độ xử lý</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">Đo lường khả năng phản xạ nhanh</p>
            </div>
            <div class="bg-purple-50 dark:bg-purple-900/30 p-6 rounded-2xl">
              <div class="text-3xl mb-2">📊</div>
              <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Phân tích chi tiết</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">Báo cáo kết quả chuyên sâu</p>
            </div>
          </div>
        </div>
        
        <button 
          id="start-test-btn"
          class="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-primary-600 to-blue-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          Bắt Đầu Test Ngay
        </button>
        
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-4">
          Mẹo: Sử dụng phím số 1-4 để chọn đáp án nhanh chóng
        </p>
      </div>
    </div>
  </div>

  <!-- Phần test sẽ được hiển thị sau khi nhấn nút bắt đầu -->
  <div id="test-section" style="display: none;">
    <IQTestWrapper 
      questions={iqQuestions} 
      timeLimit={testConfig.timeLimit}
      startImmediately={false}
      client:only="react"
    />
  </div>

  <!-- Component popup thông báo bài test dang dở (sẽ được xử lý bởi React) -->
  <div id="progress-popup-container"></div>

  <script>
    // Script để xử lý hiển thị test khi người dùng nhấn nút bắt đầu
    document.addEventListener('DOMContentLoaded', function() {
      // Thêm event listener cho nút bắt đầu test
      const startButton = document.getElementById('start-test-btn');
      if (startButton) {
        startButton.addEventListener('click', function() {
          // Ẩn header và footer trực tiếp
          const headerElement = document.querySelector('header');
          const footerElement = document.querySelector('footer');
          
          if (headerElement) {
            headerElement.style.display = 'none';
          }
          
          if (footerElement) {
            footerElement.style.display = 'none';
          }
          
          // Thêm class vào body
          document.body.classList.add('fullscreen-test');
          
          // Kích hoạt event kiểm tra xem có bài test đang làm dở không
          document.dispatchEvent(new CustomEvent('check-in-progress-test'));
        });
      }
    });
  </script>
</BaseLayout>