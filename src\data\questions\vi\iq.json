{"meta": {"title": "Test IQ <PERSON>", "description": "<PERSON><PERSON><PERSON> giá chỉ số thông minh của bạn với bài test IQ chuẩn quốc tế", "timeLimit": 3300, "totalQuestions": 10, "difficulty": "standard"}, "questions": [{"id": 1, "type": "logical_sequence", "question": "<PERSON><PERSON><PERSON> số tiếp theo trong dã<PERSON>: 2, 4, 8, 16, ?", "options": [{"id": "a", "text": "24"}, {"id": "b", "text": "32"}, {"id": "c", "text": "30"}, {"id": "d", "text": "28"}], "correct": "b", "explanation": "<PERSON><PERSON><PERSON> là cấp số nhân với công bội 2. Mỗi số gấp đôi số trước đó.", "difficulty": "easy", "points": 10}, {"id": 2, "type": "pattern_recognition", "question": "Nếu BOOK = 25, COOK = 31, thì LOOK = ?", "options": [{"id": "a", "text": "35"}, {"id": "b", "text": "37"}, {"id": "c", "text": "33"}, {"id": "d", "text": "39"}], "correct": "b", "explanation": "Mỗi chữ cái được chuyển thành số thứ tự trong bảng chữ cái và cộng lại. L=12, O=15, O=15, K=11. Tổng = 53. <PERSON><PERSON><PERSON><PERSON> có quy luật đặc biệt ở đây.", "difficulty": "medium", "points": 15}, {"id": 3, "type": "logical_reasoning", "question": "Tất cả chó đều có đuôi. Một số động vật có đuôi là mèo. Kết luận nào sau đây đúng?", "options": [{"id": "a", "text": "Tất cả mèo đều là chó"}, {"id": "b", "text": "Một số chó là mèo"}, {"id": "c", "text": "Tất cả động vật có đuôi đều là chó hoặc mèo"}, {"id": "d", "text": "<PERSON><PERSON><PERSON><PERSON> thể kết luận gì chắc chắn về mối quan hệ giữa chó và mèo"}], "correct": "d", "explanation": "Từ hai tiền đề đã cho, ta không thể kết luận gì chắc chắn về mối quan hệ trực tiếp giữa chó và mèo.", "difficulty": "medium", "points": 15}, {"id": 4, "type": "numerical_sequence", "question": "<PERSON><PERSON><PERSON> số còn thiếu: 1, 1, 2, 3, 5, 8, ?", "options": [{"id": "a", "text": "11"}, {"id": "b", "text": "13"}, {"id": "c", "text": "15"}, {"id": "d", "text": "16"}], "correct": "b", "explanation": "<PERSON><PERSON><PERSON> là dã<PERSON>. Mỗi số bằng tổng của hai số trước đó. 5 + 8 = 13.", "difficulty": "easy", "points": 10}, {"id": 5, "type": "spatial_reasoning", "question": "<PERSON><PERSON>t hình lập phương có 6 mặt. <PERSON><PERSON><PERSON> ta sơn tất cả các mặt ngoài rồi cắt thành 27 hình lập phương nhỏ bằng nhau, có bao nhiêu hình lập phương nhỏ không có mặt nào được sơn?", "options": [{"id": "a", "text": "1"}, {"id": "b", "text": "3"}, {"id": "c", "text": "6"}, {"id": "d", "text": "8"}], "correct": "a", "explanation": "<PERSON><PERSON> c<PERSON>t thành 27 khối nhỏ (3x3x3), chỉ có 1 khối ở chính giữa không chạm vào bất kỳ mặt ngoài nào.", "difficulty": "hard", "points": 20}, {"id": 6, "type": "analogy", "question": "Mắt là gì đối với nhìn như tai là gì đối với ?", "options": [{"id": "a", "text": "nghe"}, {"id": "b", "text": "<PERSON>m thanh"}, {"id": "c", "text": "n<PERSON>i"}, {"id": "d", "text": "tai nghe"}], "correct": "a", "explanation": "<PERSON><PERSON><PERSON> là cơ quan để nhìn, tai là cơ quan để nghe. <PERSON><PERSON><PERSON> là mối quan hệ cơ quan - ch<PERSON><PERSON> n<PERSON>ng.", "difficulty": "easy", "points": 10}, {"id": 7, "type": "mathematical_reasoning", "question": "Nếu 5 máy làm 5 sản phẩm trong 5 phút, thì 100 máy làm 100 sản phẩm trong bao lâu?", "options": [{"id": "a", "text": "5 phút"}, {"id": "b", "text": "100 phút"}, {"id": "c", "text": "20 phút"}, {"id": "d", "text": "10 phút"}], "correct": "a", "explanation": "Mỗi máy làm 1 sản phẩm trong 5 phút. <PERSON>, 100 máy làm 100 sản phẩm vẫn mất 5 phút.", "difficulty": "medium", "points": 15}, {"id": 8, "type": "pattern_completion", "question": "Trong dãy A, B, D, G, K, ?, chữ cái tiếp theo là gì?", "options": [{"id": "a", "text": "P"}, {"id": "b", "text": "O"}, {"id": "c", "text": "N"}, {"id": "d", "text": "M"}], "correct": "a", "explanation": "<PERSON><PERSON><PERSON><PERSON> cách gi<PERSON>a các chữ cái tăng dần: A+1=B, B+2=D, D+3=G, G+4=K, K+5=P", "difficulty": "medium", "points": 15}, {"id": 9, "type": "logical_deduction", "question": "Trong một <PERSON><PERSON> đ<PERSON>, <PERSON> về trư<PERSON>, <PERSON><PERSON><PERSON> về trư<PERSON><PERSON>, <PERSON><PERSON><PERSON> về trước An. Ai về đích đầu tiên?", "options": [{"id": "a", "text": "An"}, {"id": "b", "text": "Bình"}, {"id": "c", "text": "Cường"}, {"id": "d", "text": "Dũng"}], "correct": "d", "explanation": "T<PERSON>ứ tự: <PERSON><PERSON><PERSON> > <PERSON> > <PERSON><PERSON><PERSON> > C<PERSON><PERSON>ng. Dũng về đích đầu tiên.", "difficulty": "medium", "points": 15}, {"id": 10, "type": "abstract_reasoning", "question": "<PERSON><PERSON>u tất cả Glumps đều l<PERSON>, và một số <PERSON><PERSON> l<PERSON>, thì điều gì chắc chắn đúng?", "options": [{"id": "a", "text": "<PERSON><PERSON><PERSON> c<PERSON> Wimps đề<PERSON> là Glumps"}, {"id": "b", "text": "<PERSON><PERSON>t s<PERSON> Glumps là Wimps"}, {"id": "c", "text": "Tất cả Glumps đều có thể là Wimps"}, {"id": "d", "text": "<PERSON><PERSON><PERSON><PERSON> có <PERSON>s nào là Wimps"}], "correct": "c", "explanation": "<PERSON><PERSON> tất cả Glumps đều là <PERSON>, và một số <PERSON><PERSON><PERSON> là <PERSON>, n<PERSON><PERSON> c<PERSON> khả năng tất cả Glumps đều là Wimps.", "difficulty": "hard", "points": 20}]}