---
import BaseLayout from '../layouts/BaseLayout.astro';
import ProfileComponent from '../components/profile/ProfileComponent.tsx';

// Server-side data loading for instant display (like leaderboard)
let initialUserProfile = {
  name: '<PERSON><PERSON><PERSON><PERSON> dùng',
  age: '',
  location: '',
  totalTests: 0,
  testHistory: [],
  isAuthenticated: false
};

// Load data from server-side for instant display
try {
  // Try to load initial data (this will be fast and cached)
  
  // For now, we'll load from localStorage-like approach on server
  // This is a placeholder - in real SSR, you'd check auth headers/cookies
  
  // Set default data that will be instantly available
  initialUserProfile = {
    name: 'Ng<PERSON>ời dùng', // Will be replaced client-side if data exists
    age: '',
    location: '',
    totalTests: 0,
    testHistory: [],
    isAuthenticated: false
  };
  
} catch (error) {
  console.warn('Server-side profile loading failed:', error);
}
---

<BaseLayout 
  title="Profile - Astro IQ Test"
  description="Xem thông tin profile và lịch sử bài test IQ của bạn"
>
  <ProfileComponent client:load initialProfile={initialUserProfile} />
</BaseLayout> 