---
import SEO from './SEO.astro';

export interface Props {
  title: string;
  description: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  category?: string;
  tags?: string[];
  coverImage?: string;
  readingTime?: number;
  wordCount?: number;
  slug: string;
}

const {
  title,
  description,
  author = "IQ Test Team",
  publishedTime,
  modifiedTime,
  category = "Education",
  tags = [],
  coverImage = "/og-image.jpg",
  readingTime,
  wordCount,
  slug
} = Astro.props;

const articleUrl = `${Astro.site}blog/${slug}`;
const fullImageUrl = coverImage.startsWith('http') ? coverImage : `${Astro.site}${coverImage}`;

// Generate structured data for article
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": title,
  "description": description,
  "image": fullImageUrl,
  "url": articleUrl,
  "datePublished": publishedTime,
  "dateModified": modifiedTime || publishedTime,
  "author": {
    "@type": "Person",
    "name": author
  },
  "publisher": {
    "@type": "Organization",
    "name": "IQ Test System",
    "logo": {
      "@type": "ImageObject",
      "url": `${Astro.site}logo.png`
    }
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": articleUrl
  },
  "articleSection": category,
  "keywords": tags.join(', '),
  ...(wordCount && { "wordCount": wordCount }),
  ...(readingTime && { "timeRequired": `PT${readingTime}M` })
};
---

<SEO
  title={title}
  description={description}
  keywords={tags.join(', ')}
  image={coverImage}
  url={articleUrl}
  type="article"
  author={author}
  publishedTime={publishedTime}
  modifiedTime={modifiedTime}
/>

<!-- Enhanced Article Structured Data -->
<script type="application/ld+json" set:html={JSON.stringify(structuredData)} />

<!-- Additional Article Meta Tags -->
<meta name="article:author" content={author} />
<meta name="article:section" content={category} />
<meta name="article:published_time" content={publishedTime} />
{modifiedTime && <meta name="article:modified_time" content={modifiedTime} />}
{tags.map(tag => <meta name="article:tag" content={tag} />)}

<!-- Reading Time & Word Count -->
{readingTime && <meta name="twitter:label1" content="Reading time" />}
{readingTime && <meta name="twitter:data1" content={`${readingTime} min read`} />}
{wordCount && <meta name="twitter:label2" content="Word count" />}
{wordCount && <meta name="twitter:data2" content={`${wordCount} words`} />} 