import { supabase, TABLES } from '../config/supabase';

export interface UserWithProfile {
  id: string;
  email: string;
  email_confirmed_at: string | null;
  created_at: string;
  last_sign_in_at: string | null;
  full_name: string;
  role: 'user' | 'admin' | 'mod';
  is_verified: boolean;
  last_login: string | null;
  age?: number;
  location?: string;
  user_type?: 'registered' | 'anonymous';
}

export interface UsersListResponse {
  users: UserWithProfile[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface UsersFilters {
  role?: 'user' | 'admin' | 'mod' | 'all' | 'anonymous';
  search?: string;
  verified?: boolean;
  user_type?: 'registered' | 'anonymous';
}

/**
 * Users Management Service for Admin
 */
export class UsersService {

  /**
   * Get paginated users list with filters (bao gồm cả anonymous players)
   */
  static async getUsers(
    page: number = 1,
    limit: number = 5,
    filters: UsersFilters = {}
  ): Promise<{ data: UsersListResponse | null; error: any }> {
    try {
      console.log('UsersService: Fetching users (registered + anonymous)', { page, limit, filters });

      const offset = (page - 1) * limit;

      // Fetch registered users từ RPC function hiện tại
      const rpcParams = {
        page_limit: Math.max(limit * 2, 10), // Lấy nhiều hơn để có đủ dữ liệu khi merge
        page_offset: 0, // Reset offset vì sẽ handle pagination sau khi merge
        role_filter: filters.role === 'all' ? null : filters.role,
        search_term: filters.search || null,
        verified_filter: filters.verified
      };

      console.log('UsersService: Calling get_users_with_email RPC function...');
      const [registeredResult, anonymousResult] = await Promise.all([
        supabase.rpc('get_users_with_email', rpcParams),
        // Query anonymous users from user_test_results where user_id is NULL
        supabase
          .from('user_test_results')
          .select('id, guest_name, guest_age, guest_location, tested_at')
          .is('user_id', null)
          .not('guest_name', 'is', null)
          .order('tested_at', { ascending: false })
      ]);

      const { data: registeredUsers, error: rpcError } = registeredResult;
      const { data: anonymousTestResults, error: anonymousError } = anonymousResult;

      if (rpcError) {
        console.error('UsersService: Error fetching registered users:', rpcError);
        return { data: null, error: rpcError };
      }

      if (anonymousError) {
        console.error('UsersService: Error fetching anonymous users:', anonymousError);
        return { data: null, error: anonymousError };
      }

      // Group anonymous test results by guest info to get unique users
      const uniqueAnonymousUsers = new Map();
      (anonymousTestResults || []).forEach(result => {
        const key = `${result.guest_name}-${result.guest_age}-${result.guest_location}`;
        if (!uniqueAnonymousUsers.has(key)) {
          uniqueAnonymousUsers.set(key, {
            id: result.id,
            name: result.guest_name,
            age: result.guest_age,
            location: result.guest_location,
            created_at: result.tested_at
          });
        }
      });

      // Transform anonymous users thành UserWithProfile format
      const transformedAnonymous: UserWithProfile[] = Array.from(uniqueAnonymousUsers.values()).map(user => ({
        id: user.id,
        email: `anonymous-${user.id.slice(0, 8)}@anonymous.local`, // Email placeholder
        email_confirmed_at: null,
        created_at: user.created_at,
        last_sign_in_at: null,
        full_name: user.name,
        role: 'user' as const,
        is_verified: false,
        last_login: null,
        age: user.age,
        location: user.location,
        user_type: 'anonymous' as const
      }));

      // Transform registered users và thêm user_type
      const transformedRegistered: UserWithProfile[] = (registeredUsers || []).map((user: any) => ({
        ...user,
        user_type: 'registered' as const
      }));

      // Merge cả 2 lists
      let allUsers = [...transformedRegistered, ...transformedAnonymous];

      // Apply search filter nếu có
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        allUsers = allUsers.filter(user => 
          user.full_name.toLowerCase().includes(searchTerm) ||
          user.email.toLowerCase().includes(searchTerm) ||
          (user.location && user.location.toLowerCase().includes(searchTerm))
        );
      }

      // Apply role filter nếu có
      if (filters.role && filters.role !== 'all') {
        if (filters.role === 'anonymous') {
          allUsers = allUsers.filter(user => user.user_type === 'anonymous');
        } else {
          allUsers = allUsers.filter(user => user.role === filters.role && user.user_type === 'registered');
        }
      }

      // Apply verified filter nếu có  
      if (filters.verified !== undefined) {
        allUsers = allUsers.filter(user => user.is_verified === filters.verified);
      }

      // Apply user type filter nếu có
      if (filters.user_type) {
        allUsers = allUsers.filter(user => user.user_type === filters.user_type);
      }

      // Sort by created_at desc
      allUsers.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      // Apply pagination
      const total = allUsers.length;
      const startIndex = offset;
      const endIndex = startIndex + limit;
      const paginatedUsers = allUsers.slice(startIndex, endIndex);

      const totalPages = Math.ceil(total / limit);

      const result: UsersListResponse = {
        users: paginatedUsers,
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      };

      console.log('UsersService: Users fetched successfully:', {
        registered: transformedRegistered.length,
        anonymous: transformedAnonymous.length,
        total,
        page,
        totalPages,
        returned: paginatedUsers.length
      });

      return { data: result, error: null };

    } catch (err) {
      console.error('UsersService: Unexpected error:', err);
      return { data: null, error: err };
    }
  }

  /**
   * Update user role
   */
  static async updateUserRole(
    userId: string, 
    newRole: 'user' | 'admin' | 'mod'
  ): Promise<{ success: boolean; error: any }> {
    try {
      console.log('UsersService: Updating user role:', { userId, newRole });

      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .update({ 
          role: newRole,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        console.error('UsersService: Error updating user role:', error);
        return { success: false, error };
      }

      console.log('UsersService: User role updated successfully');
      return { success: true, error: null };

    } catch (err) {
      console.error('UsersService: Unexpected error updating role:', err);
      return { success: false, error: err };
    }
  }

  /**
   * Toggle user verification status
   */
  static async toggleUserVerification(
    userId: string
  ): Promise<{ success: boolean; error: any }> {
    try {
      console.log('UsersService: Toggling user verification:', userId);

      // Get current status first
      const { data: currentUser, error: fetchError } = await supabase
        .from(TABLES.PROFILES)
        .select('is_verified')
        .eq('id', userId)
        .single();

      if (fetchError) {
        console.error('UsersService: Error fetching current user:', fetchError);
        return { success: false, error: fetchError };
      }

      const newVerifiedStatus = !currentUser.is_verified;

      const { data, error } = await supabase
        .from(TABLES.PROFILES)
        .update({ 
          is_verified: newVerifiedStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        console.error('UsersService: Error updating verification:', error);
        return { success: false, error };
      }

      console.log('UsersService: User verification updated:', newVerifiedStatus);
      return { success: true, error: null };

    } catch (err) {
      console.error('UsersService: Unexpected error toggling verification:', err);
      return { success: false, error: err };
    }
  }

  /**
   * Get user statistics (bao gồm cả anonymous players)
   */
  static async getUserStats(): Promise<{ 
    data: { 
      total: number; 
      admins: number; 
      mods: number; 
      users: number; 
      verified: number; 
      unverified: number;
      registered: number;
      anonymous: number;
    } | null; 
    error: any 
  }> {
    try {
      console.log('UsersService: Fetching user statistics (registered + anonymous)');

      // Fetch both registered users and anonymous test results
      const [registeredResult, anonymousResult] = await Promise.all([
        supabase.from(TABLES.PROFILES).select('role, is_verified'),
        // Count unique anonymous users from user_test_results
        supabase
          .from('user_test_results')
          .select('guest_name, guest_age, guest_location')
          .is('user_id', null)
          .not('guest_name', 'is', null)
      ]);

      const { data: registeredData, error: registeredError } = registeredResult;
      const { data: anonymousTestResults, error: anonymousError } = anonymousResult;

      if (registeredError) {
        console.error('UsersService: Error fetching registered stats:', registeredError);
        return { data: null, error: registeredError };
      }

      if (anonymousError) {
        console.error('UsersService: Error fetching anonymous stats:', anonymousError);
        return { data: null, error: anonymousError };
      }

      const registeredCount = registeredData?.length || 0;
      
      // Count unique anonymous users
      const uniqueAnonymousUsers = new Set();
      (anonymousTestResults || []).forEach(result => {
        const key = `${result.guest_name}-${result.guest_age}-${result.guest_location}`;
        uniqueAnonymousUsers.add(key);
      });
      const anonymousCount = uniqueAnonymousUsers.size;

      const stats = {
        total: registeredCount + anonymousCount,
        admins: registeredData?.filter(u => u.role === 'admin').length || 0,
        mods: registeredData?.filter(u => u.role === 'mod').length || 0,
        users: (registeredData?.filter(u => u.role === 'user').length || 0) + anonymousCount, // All anonymous are users
        verified: registeredData?.filter(u => u.is_verified).length || 0,
        unverified: (registeredData?.filter(u => !u.is_verified).length || 0) + anonymousCount, // All anonymous are unverified
        registered: registeredCount,
        anonymous: anonymousCount
      };

      console.log('UsersService: Stats fetched:', stats);
      return { data: stats, error: null };

    } catch (err) {
      console.error('UsersService: Error fetching stats:', err);
      return { data: null, error: err };
    }
  }
} 