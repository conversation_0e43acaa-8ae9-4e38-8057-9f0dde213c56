{"name": "astro-iq-test", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "setup": "node scripts/setup.js", "check": "astro check", "lint": "astro check", "clean": "rm -rf dist .astro node_modules/.cache", "deploy": "npm run build && npm run preview"}, "dependencies": {"@astrojs/react": "^3.6.2", "@astrojs/sitemap": "^3.1.6", "@astrojs/tailwind": "^5.1.0", "@supabase/supabase-js": "^2.50.2", "@toast-ui/editor": "^3.2.2", "@toast-ui/react-editor": "^3.2.3", "@types/canvas-confetti": "^1.6.4", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "astro": "^4.15.0", "astro-seo": "^0.8.4", "canvas-confetti": "^1.9.3", "framer-motion": "^11.5.0", "lucide": "^0.523.0", "react": "^18.3.0", "react-dom": "^18.3.0", "tailwindcss": "^3.4.0"}, "devDependencies": {"@astrojs/check": "^0.9.0", "typescript": "^5.5.0"}}