---
import BaseLayout from '../../layouts/BaseLayout.astro';
import EQTestWrapper from '../../components/EQTestWrapper.tsx';

// Simple locale function
const getLocale = (lang: string) => ({
  meta: {
    site_name: 'IQ Test Pro'
  }
});

const locale = getLocale('vi');
---

<BaseLayout
  title={`Test EQ - Trắc nghiệm chỉ số cảm xúc - ${locale.meta.site_name}`}
  description="Đánh giá chỉ số cảm xúc (EQ) của bạn với bài test khoa học. Khám phá khả năng hiểu và quản lý cảm xúc."
>
  <section class="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-100 py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      
      <!-- Header -->
      <div class="text-center mb-12">
        <div class="w-24 h-24 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
          <!-- Heart SVG Icon -->
          <svg width="40" height="40" viewBox="0 0 24 24" fill="none" class="text-white">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" fill="currentColor"/>
          </svg>
        </div>
        
        <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-4">
          Test <span class="text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600">EQ</span>
        </h1>
        
        <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Đánh giá chỉ số cảm xúc (Emotional Quotient) của bạn - khả năng hiểu và quản lý cảm xúc bản thân cũng như người khác
        </p>

        <!-- EQ Info Cards -->
        <div class="grid md:grid-cols-3 gap-6 mb-12">
          <div class="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
            <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <!-- Self-awareness Brain SVG -->
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="text-pink-600">
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                <path d="M12 1v6m0 6v6" stroke="currentColor" stroke-width="2"/>
                <path d="M21 12h-6m-6 0H3" stroke="currentColor" stroke-width="2"/>
                <path d="M18.36 6.64l-4.24 4.24m-4.24 0L5.64 6.64" stroke="currentColor" stroke-width="1.5"/>
                <path d="M18.36 17.36l-4.24-4.24m-4.24 0L5.64 17.36" stroke="currentColor" stroke-width="1.5"/>
              </svg>
            </div>
            <h3 class="font-bold text-gray-900 mb-2">Tự nhận thức</h3>
            <p class="text-sm text-gray-600">Hiểu biết về cảm xúc và phản ứng của bản thân</p>
          </div>
          
          <div class="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <!-- Social People SVG -->
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="text-purple-600">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke="currentColor" stroke-width="2"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="2"/>
                <circle cx="9" cy="7" r="1" fill="currentColor"/>
                <circle cx="19" cy="7" r="1" fill="currentColor"/>
              </svg>
            </div>
            <h3 class="font-bold text-gray-900 mb-2">Kỹ năng xã hội</h3>
            <p class="text-sm text-gray-600">Khả năng giao tiếp và xây dựng mối quan hệ</p>
          </div>
          
          <div class="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
            <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <!-- Control/Balance SVG -->
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="text-indigo-600">
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" stroke="currentColor" stroke-width="2"/>
                <path d="M16 8l-6 6" stroke="currentColor" stroke-width="1" opacity="0.5"/>
                <path d="M8 8l6 6" stroke="currentColor" stroke-width="1" opacity="0.5"/>
              </svg>
            </div>
            <h3 class="font-bold text-gray-900 mb-2">Tự điều chỉnh</h3>
            <p class="text-sm text-gray-600">Kiểm soát và điều chỉnh cảm xúc hiệu quả</p>
          </div>
        </div>
      </div>

      <!-- Test Info Card with integrated button -->
      <div class="bg-white rounded-3xl shadow-lg p-8 mb-8">
        <div class="grid lg:grid-cols-2 gap-8 items-center">
          <div>
            <h2 class="text-3xl font-bold text-gray-900 mb-6">Bài test EQ chuẩn quốc tế</h2>
            <div class="space-y-4 mb-8">
              <div class="flex items-center">
                <!-- Clock SVG -->
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="text-pink-600 mr-3">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                  <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                <span class="text-gray-700">Thời gian: <strong>25-30 phút</strong></span>
              </div>
              <div class="flex items-center">
                <!-- Question SVG -->
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="text-purple-600 mr-3">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                  <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 17h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="text-gray-700">Số câu hỏi: <strong>60 câu</strong></span>
              </div>
              <div class="flex items-center">
                <!-- Award SVG -->
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="text-indigo-600 mr-3">
                  <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M4 22h16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  <path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M18 2H6v7a6 6 0 0 0 12 0V2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="text-gray-700">Kết quả: <strong>Phân tích chi tiết</strong></span>
              </div>
              <div class="flex items-center">
                <!-- Shield Check SVG -->
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="text-green-600 mr-3">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="text-gray-700">Độ chính xác: <strong>95%</strong></span>
              </div>
            </div>

            <!-- Start Test Button moved inside card -->
            <button 
              onclick="startEQTest()"
              class="group w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold text-lg px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 flex items-center justify-center"
            >
              <!-- Play SVG -->
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="mr-3">
                <polygon points="5,3 19,12 5,21" fill="currentColor"/>
              </svg>
              <span>Bắt đầu Test EQ ngay</span>
              <!-- Arrow SVG -->
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="ml-3 group-hover:translate-x-1 transition-transform duration-300">
                <path d="M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 5l7 7-7 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>

          <div class="text-center">
            <div class="bg-gradient-to-r from-pink-100 to-purple-100 rounded-2xl p-8 relative overflow-hidden">
              <!-- Background decoration -->
              <div class="absolute inset-0 bg-gradient-to-br from-pink-200/20 to-purple-200/20 rounded-2xl"></div>
              <div class="relative z-10">
                <div class="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600 mb-2">
                  EQ
                </div>
                <p class="text-gray-600 text-sm font-medium">Emotional Quotient</p>
                <p class="text-xs text-gray-500 mt-2">Chỉ số cảm xúc</p>
                
                <!-- Decorative elements -->
                <div class="mt-6 flex justify-center space-x-2">
                  <div class="w-2 h-2 bg-pink-400 rounded-full animate-pulse"></div>
                  <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                  <div class="w-2 h-2 bg-indigo-400 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Trust indicators moved to bottom of card -->
        <div class="mt-8 pt-6 border-t border-gray-100">
          <div class="flex items-center justify-center gap-8 text-sm text-gray-500">
            <div class="flex items-center">
              <!-- Shield SVG -->
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="mr-2 text-green-500">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="currentColor" stroke-width="2" fill="none"/>
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              Miễn phí 100%
            </div>
            <div class="flex items-center">
              <!-- Lightning SVG -->
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="mr-2 text-yellow-500">
                <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2" fill="currentColor"/>
              </svg>
              Kết quả tức thì
            </div>
            <div class="flex items-center">
              <!-- Certificate SVG -->
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="mr-2 text-blue-500">
                <circle cx="12" cy="8" r="7" stroke="currentColor" stroke-width="2" fill="none"/>
                <path d="M8.21 13.89L7 23l5-3 5 3-1.21-9.12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              Chứng chỉ quốc tế
            </div>
          </div>
        </div>
      </div>

      <!-- Back to Home Button -->
      <div class="text-center">
        <a 
          href="/" 
          class="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
        >
          <!-- Arrow Left SVG -->
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="mr-2">
            <path d="M19 12H5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Quay lại trang chủ
        </a>
      </div>

    </div>
  </section>
</BaseLayout>

<script>
  function startEQTest() {
    // Show loading state
    const button = event.target.closest('button');
    button.innerHTML = `
      <svg class="animate-spin h-6 w-6 text-white mr-3" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span>Đang chuẩn bị bài test...</span>
    `;
    
    button.disabled = true;
    
    // Simulate loading and redirect to coming soon or actual test
    setTimeout(() => {
      alert('🚧 Test EQ đang được phát triển!\n\nChúng tôi sẽ sớm ra mắt bài test EQ hoàn chỉnh. Hãy theo dõi để không bỏ lỡ!');
      
      // Reset button
      button.innerHTML = `
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="mr-3">
          <polygon points="5,3 19,12 5,21" fill="currentColor"/>
        </svg>
        <span>Bắt đầu Test EQ ngay</span>
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="ml-3">
          <path d="M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 5l7 7-7 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      `;
      button.disabled = false;
    }, 2000);
  }
</script> 