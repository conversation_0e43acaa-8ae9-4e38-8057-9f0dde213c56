---
import BaseLayout from '../layouts/BaseLayout.astro';

// Get test result from URL params
const url = new URL(Astro.request.url);
const userName = url.searchParams.get('name') || '';
const userAge = url.searchParams.get('age') || '';
const userLocation = url.searchParams.get('location') || '';
const testScore = parseInt(url.searchParams.get('score') || '100');
const testPercentile = parseInt(url.searchParams.get('percentile') || '50');
const testAccuracy = parseInt(url.searchParams.get('accuracy') || '80');
const testTime = parseInt(url.searchParams.get('time') || '1800');

// SEO data based on score
const seoTitle = `Kết quả IQ: ${testScore} điểm - Vượt qua ${testPercentile}% dân số`;
const seoDescription = `${userName ? userName + ' đạt' : 'Đạt'} ${testScore} điểm IQ, vượt qua ${testPercentile}% dân số thế giới. Thử sức với bài test IQ chuyên nghiệp!`;
---

<BaseLayout
  title={seoTitle}
  description={seoDescription}
>
  <div id="result-container" class="min-h-screen" 
       data-user-name={userName} 
       data-user-age={userAge} 
       data-user-location={userLocation}
       data-user-email={url.searchParams.get('email') || ''}>
    <!-- Loading skeleton -->
    <div id="loading-skeleton" class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Main Card Skeleton -->
        <div class="bg-white rounded-3xl shadow-md p-8 mb-8 animate-pulse">
          <div class="flex justify-between items-center mb-8">
                      <div class="flex items-center">
              <div class="w-16 h-16 bg-gray-300 rounded-full mr-4"></div>
              <div>
                <div class="h-6 bg-gray-300 rounded w-24 mb-2"></div>
                <div class="h-4 bg-gray-300 rounded w-32"></div>
              </div>
            </div>
            <div class="flex space-x-4">
              <div class="h-16 w-16 bg-gray-300 rounded-xl"></div>
              <div class="h-16 w-16 bg-gray-300 rounded-xl"></div>
              <div class="h-16 w-16 bg-gray-300 rounded-xl"></div>
            </div>
          </div>
          
          <!-- Score Skeleton -->
          <div class="text-center mb-8">
            <div class="h-24 w-48 bg-gray-300 rounded mx-auto mb-4"></div>
            <div class="h-6 w-40 bg-gray-300 rounded mx-auto mb-6"></div>
            <div class="h-3 w-80 bg-gray-300 rounded mx-auto"></div>
          </div>
          
          <!-- Buttons Skeleton -->
          <div class="grid grid-cols-2 lg:grid-cols-5 gap-3">
            <div class="h-12 bg-gray-300 rounded-xl"></div>
            <div class="h-12 bg-gray-300 rounded-xl"></div>
            <div class="h-12 bg-gray-300 rounded-xl"></div>
            <div class="h-12 bg-gray-300 rounded-xl"></div>
            <div class="h-12 bg-gray-300 rounded-xl"></div>
          </div>
        </div>
        
        <!-- Additional Cards Skeleton -->
        <div class="bg-white rounded-2xl shadow-md p-8 mb-8 animate-pulse">
          <div class="h-8 bg-gray-300 rounded w-64 mb-6"></div>
        <div class="grid lg:grid-cols-2 gap-8">
            <div class="space-y-3">
              <div class="h-4 bg-gray-300 rounded"></div>
              <div class="h-4 bg-gray-300 rounded"></div>
              <div class="h-4 bg-gray-300 rounded"></div>
              <div class="h-4 bg-gray-300 rounded"></div>
            </div>
            <div class="space-y-3">
              <div class="h-4 bg-gray-300 rounded"></div>
              <div class="h-4 bg-gray-300 rounded"></div>
              <div class="h-4 bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-2xl shadow-md p-8 animate-pulse">
          <div class="h-8 bg-gray-300 rounded w-48 mb-6"></div>
        <div class="grid lg:grid-cols-2 gap-8">
            <div class="h-64 bg-gray-300 rounded"></div>
          <div class="space-y-4">
              <div class="h-4 bg-gray-300 rounded"></div>
              <div class="h-4 bg-gray-300 rounded"></div>
              <div class="h-4 bg-gray-300 rounded"></div>
              <div class="h-4 bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</BaseLayout>

<script>
  import { createRoot } from 'react-dom/client';
  import { createElement } from 'react';
  
  // Load result data from localStorage or URL params
  document.addEventListener('DOMContentLoaded', () => {
    let resultData = null;
    
    // Try to get from localStorage first (fresh completion)
    try {
      const savedResult = localStorage.getItem('current-test-result');
      if (savedResult) {
        resultData = JSON.parse(savedResult);
        localStorage.removeItem('current-test-result');
        console.log('📊 Loaded fresh result from localStorage');
      }
    } catch (error) {
      console.warn('Error loading from localStorage:', error);
    }
    
    // If no localStorage data, create from URL params (shared link)
    if (!resultData) {
      const urlParams = new URLSearchParams(window.location.search);
      resultData = {
        iq: parseInt(urlParams.get('score') || '100'),
        score: parseInt(urlParams.get('raw_score') || '15'),
        percentile: parseInt(urlParams.get('percentile') || '50'),
        timeSpent: parseInt(urlParams.get('time') || '1800'),
        detailed: {
          correct: parseInt(urlParams.get('correct') || '15'),
          incorrect: parseInt(urlParams.get('incorrect') || '5'),
          accuracy: parseInt(urlParams.get('accuracy') || '75')
        },
        classification: urlParams.get('classification') || 'average',
        answers: [],
        categoryScores: {}
      };
      console.log('🔗 Created result from URL params for shared link');
    }
    
    // Convert to ResultComponent format
    const resultComponentData = convertToResultData(resultData);
    
    // Render ResultComponent
    renderResultComponent(resultComponentData);
  });
  
  function convertToResultData(result) {
    // Mock questions data for display
    const mockQuestions = Array.from({length: 20}, (_, i) => ({
      id: i + 1,
      type: ['logic', 'math', 'spatial', 'pattern'][i % 4],
      difficulty: ['easy', 'medium', 'hard'][i % 3],
      question: `Question ${i + 1}`,
      options: ['A', 'B', 'C', 'D'],
      correct: i % 4,
      explanation: `Explanation for question ${i + 1}`
    }));
    
    return {
      score: result.iq || result.score || 100,
      rawScore: result.score || 15,
      maxScore: 20,
      correctAnswers: result.detailed?.correct || 15,
      totalQuestions: 20,
      percentile: result.percentile || 50,
      classification: {
        level: getClassificationLevel(result.classification || 'average'),
        color: getClassificationColor(result.classification || 'average'),
        description: getClassificationDescription(result.classification || 'average')
      },
      timeTaken: result.timeSpent || 1800,
      timeLimit: 1800,
      answerDetails: mockQuestions.map((q, index) => ({
        questionId: q.id,
        question: q.question,
        userAnswer: result.answers?.[index] ?? (index % 4),
        correctAnswer: q.correct,
        isCorrect: (result.answers?.[index] ?? (index % 4)) === q.correct,
        explanation: q.explanation,
        points: (result.answers?.[index] ?? (index % 4)) === q.correct ? 1 : 0,
        maxPoints: 1,
        difficulty: q.difficulty,
        type: q.type
      })),
      completionRate: (result.detailed?.accuracy || 75) / 100
    };
  }
  
  function getClassificationLevel(classification) {
    const levels = {
      'genius': 'Thiên tài',
      'very_superior': 'Rất cao', 
      'superior': 'Cao',
      'high_average': 'Khá cao',
      'average': 'Trung bình',
      'low_average': 'Dưới trung bình',
      'borderline': 'Thấp',
      'low': 'Rất thấp'
    };
    return levels[classification] || 'Trung bình';
  }
  
  function getClassificationColor(classification) {
    const colors = {
      'genius': 'purple',
      'very_superior': 'blue',
      'superior': 'green', 
      'high_average': 'green',
      'average': 'yellow',
      'low_average': 'orange',
      'borderline': 'red',
      'low': 'red'
    };
    return colors[classification] || 'yellow';
  }
  
  function getClassificationDescription(classification) {
    const descriptions = {
      'genius': 'Chỉ số IQ vượt trội',
      'very_superior': 'Trí thông minh vượt trội',
      'superior': 'Trên mức trung bình cao',
      'high_average': 'Trên mức trung bình',
      'average': 'Mức trung bình',
      'low_average': 'Dưới mức trung bình', 
      'borderline': 'Cần cải thiện',
      'low': 'Cần cải thiện nhiều'
    };
    return descriptions[classification] || 'Mức trung bình';
  }
  
  async function renderResultComponent(resultData) {
    try {
      // Dynamic import ResultComponent
      const { default: ResultComponent } = await import('@/components/tests/results/ResultComponent.tsx');
      
      const container = document.getElementById('result-container');
      if (!container) return;
      
      // Get user info from URL params directly (more reliable than data attributes)
      const urlParams = new URLSearchParams(window.location.search);
      const userInfo = {
        name: urlParams.get('name') || '',
        email: urlParams.get('email') || '',
        age: urlParams.get('age') || '',
        location: urlParams.get('location') || ''
      };
      
      console.log('📋 User info from URL params:', userInfo);
      
      const root = createRoot(container);
      
      const props = {
        results: resultData,
        userInfo: userInfo.name ? userInfo : null, // Only pass if we have name
        onRetake: () => window.location.href = '/test/iq',
        onHome: () => window.location.href = '/'
      };
      
      root.render(createElement(ResultComponent, props));
      
      // Hide skeleton with smooth transition
      const skeleton = document.getElementById('loading-skeleton');
      if (skeleton) {
        skeleton.style.opacity = '0';
        skeleton.style.transition = 'opacity 0.3s ease-out';
        setTimeout(() => {
          skeleton.style.display = 'none';
        }, 300);
      }
      
      console.log('✅ ResultComponent rendered successfully');
      
    } catch (error) {
      console.error('❌ Error rendering ResultComponent:', error);
      
      // Hide skeleton
      const skeleton = document.getElementById('loading-skeleton');
      if (skeleton) {
        skeleton.style.opacity = '0';
        setTimeout(() => skeleton.style.display = 'none', 300);
      }
      
      // Fallback display
      document.getElementById('result-container').innerHTML = `
        <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
          <div class="text-center bg-white rounded-2xl p-8 shadow-xl">
            <h1 class="text-4xl font-bold mb-4">Kết quả IQ: ${resultData.score}</h1>
            <p class="text-xl mb-4">Vượt qua ${resultData.percentile}% dân số</p>
            <button onclick="window.location.href='/test/iq'" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">Làm lại test</button>
          </div>
        </div>
      `;
    }
  }
</script>