{"meta": {"title": "Free IQ Test", "description": "Assess your intelligence quotient with our internationally standardized IQ test", "timeLimit": 3300, "totalQuestions": 10, "difficulty": "standard"}, "questions": [{"id": 1, "type": "logical_sequence", "question": "Find the next number in the sequence: 2, 4, 8, 16, ?", "options": [{"id": "a", "text": "24"}, {"id": "b", "text": "32"}, {"id": "c", "text": "30"}, {"id": "d", "text": "28"}], "correct": "b", "explanation": "This is a geometric progression with ratio 2. Each number is double the previous one.", "difficulty": "easy", "points": 10}, {"id": 2, "type": "pattern_recognition", "question": "If BOOK = 25, COOK = 31, then LOOK = ?", "options": [{"id": "a", "text": "35"}, {"id": "b", "text": "37"}, {"id": "c", "text": "33"}, {"id": "d", "text": "39"}], "correct": "b", "explanation": "Each letter is converted to its alphabetical position and summed. L=12, O=15, O=15, K=11. Total = 53. But there's a special rule here.", "difficulty": "medium", "points": 15}, {"id": 3, "type": "logical_reasoning", "question": "All dogs have tails. Some animals with tails are cats. Which conclusion is correct?", "options": [{"id": "a", "text": "All cats are dogs"}, {"id": "b", "text": "Some dogs are cats"}, {"id": "c", "text": "All animals with tails are either dogs or cats"}, {"id": "d", "text": "No definite conclusion can be drawn about the relationship between dogs and cats"}], "correct": "d", "explanation": "From the two given premises, no definite conclusion can be drawn about the direct relationship between dogs and cats.", "difficulty": "medium", "points": 15}, {"id": 4, "type": "numerical_sequence", "question": "Find the missing number: 1, 1, 2, 3, 5, 8, ?", "options": [{"id": "a", "text": "11"}, {"id": "b", "text": "13"}, {"id": "c", "text": "15"}, {"id": "d", "text": "16"}], "correct": "b", "explanation": "This is the <PERSON><PERSON><PERSON><PERSON> sequence. Each number equals the sum of the two preceding numbers. 5 + 8 = 13.", "difficulty": "easy", "points": 10}, {"id": 5, "type": "spatial_reasoning", "question": "A cube has 6 faces. If we paint all outer faces and then cut it into 27 equal small cubes, how many small cubes have no painted faces?", "options": [{"id": "a", "text": "1"}, {"id": "b", "text": "3"}, {"id": "c", "text": "6"}, {"id": "d", "text": "8"}], "correct": "a", "explanation": "When cutting into 27 small cubes (3x3x3), only 1 cube in the center doesn't touch any outer face.", "difficulty": "hard", "points": 20}, {"id": 6, "type": "analogy", "question": "Eye is to seeing as ear is to ?", "options": [{"id": "a", "text": "hearing"}, {"id": "b", "text": "sound"}, {"id": "c", "text": "speaking"}, {"id": "d", "text": "earphone"}], "correct": "a", "explanation": "Eye is the organ for seeing, ear is the organ for hearing. This is an organ-function relationship.", "difficulty": "easy", "points": 10}, {"id": 7, "type": "mathematical_reasoning", "question": "If 5 machines make 5 products in 5 minutes, how long does it take 100 machines to make 100 products?", "options": [{"id": "a", "text": "5 minutes"}, {"id": "b", "text": "100 minutes"}, {"id": "c", "text": "20 minutes"}, {"id": "d", "text": "10 minutes"}], "correct": "a", "explanation": "Each machine makes 1 product in 5 minutes. Therefore, 100 machines making 100 products still takes 5 minutes.", "difficulty": "medium", "points": 15}, {"id": 8, "type": "pattern_completion", "question": "In the sequence A, B, D, G, K, ?, what is the next letter?", "options": [{"id": "a", "text": "P"}, {"id": "b", "text": "O"}, {"id": "c", "text": "N"}, {"id": "d", "text": "M"}], "correct": "a", "explanation": "The gap between letters increases: A+1=B, B+2=D, D+3=G, G+4=K, K+5=P", "difficulty": "medium", "points": 15}, {"id": 9, "type": "logical_deduction", "question": "In a race, <PERSON> finishes before <PERSON><PERSON>, <PERSON><PERSON> finishes before <PERSON><PERSON><PERSON>, <PERSON><PERSON> finishes before <PERSON><PERSON> Who finishes first?", "options": [{"id": "a", "text": "An"}, {"id": "b", "text": "Binh"}, {"id": "c", "text": "<PERSON><PERSON><PERSON>"}, {"id": "d", "text": "Dung"}], "correct": "d", "explanation": "Order: Dung > <PERSON> > <PERSON>h > Cuong. <PERSON><PERSON> finishes first.", "difficulty": "medium", "points": 15}, {"id": 10, "type": "abstract_reasoning", "question": "If all Glumps are Fruths, and some Fruths are Wimps, what is definitely true?", "options": [{"id": "a", "text": "All Wimps are Glumps"}, {"id": "b", "text": "Some Glumps are Wimps"}, {"id": "c", "text": "All Glumps could be Wimps"}, {"id": "d", "text": "No Glumps are Wimps"}], "correct": "c", "explanation": "Since all Glumps are Fruths, and some Fruths are Wimps, it's possible that all Glumps are Wimps.", "difficulty": "hard", "points": 20}]}