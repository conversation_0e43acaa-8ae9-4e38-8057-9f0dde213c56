---
import BaseLayout from '../layouts/BaseLayout.astro';

const locale = {
  meta: {
    site_name: "Test IQ Miễn <PERSON> - <PERSON>ra Chỉ Số Thông <PERSON>",
    description: "Thử thách trí tuệ với bài test IQ chuẩn quốc tế. 10 câu hỏi, 55 phút, kết quả chi tiết và phân tích chuyên sâu.",
    keywords: "test iq, kiểm tra thông minh, tr<PERSON> tu<PERSON>, iq test việt nam"
  }
};
---

<BaseLayout
  title={locale.meta.site_name}
  description={locale.meta.description}
  keywords={locale.meta.keywords}
>
  <!-- Hero Section -->
  <section class="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-16 md:py-24">
    <div class="max-w-6xl mx-auto px-6 text-center relative z-10">
      <!-- <PERSON>ge -->
      <div class="inline-flex items-center px-4 py-2 bg-white rounded-full shadow-sm border border-blue-100 text-blue-700 text-sm font-medium mb-8">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
          <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/>
          <path d="M5 3v4"/>
          <path d="M19 17v4"/>
          <path d="M3 5h4"/>
          <path d="M17 19h4"/>
        </svg>
        AI Technology • 5M+ Users Worldwide
      </div>

      <!-- Main Title -->
      <h1 class="text-5xl md:text-6xl font-bold mb-6 text-gray-900" style="line-height: 1.3;">
        Test IQ & EQ 
        <span class="text-transparent bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text">Chính Xác Nhất</span>
        Thế Giới
      </h1>

      <!-- Subtitle -->
      <p class="text-xl text-gray-600 max-w-2xl mx-auto mb-10 leading-relaxed">
        Khám phá toàn diện trí tuệ IQ và EQ với công nghệ AI tiên tiến. 
        Kết quả tức thì, phân tích khoa học.
      </p>

      <!-- CTA Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
        <a href="/test/iq" class="group px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] flex items-center justify-center">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
            <polyline points="7.5,12 12,15.5 16.5,12"/>
            <polyline points="7.5,8 12,11.5 16.5,8"/>
          </svg>
          Bắt đầu Test IQ
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 group-hover:translate-x-1 transition-transform">
            <polyline points="9,18 15,12 9,6"/>
          </svg>
        </a>
        <a href="/leaderboard" class="px-8 py-4 bg-white text-gray-700 rounded-xl font-semibold shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 flex items-center justify-center">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
            <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"/>
            <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"/>
            <path d="M4 22h16"/>
            <path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"/>
            <path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"/>
            <path d="M18 2H6v7a6 6 0 0 0 12 0V2z"/>
          </svg>
          Bảng Xếp Hạng
        </a>
      </div>

      <!-- Trust Indicators -->
      <div class="flex items-center justify-center gap-8 text-sm text-gray-500">
        <div class="flex items-center">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1 text-green-500">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
            <polyline points="9,12 10.5,13.5 15,9"/>
          </svg>
          Bảo mật SSL
        </div>
        <div class="flex items-center">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1 text-yellow-500">
            <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
          </svg>
          Kết quả tức thì
        </div>
        <div class="flex items-center">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1 text-blue-500">
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26 12,2"/>
          </svg>
          Độ chính xác 99.7%
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="py-20 bg-white">
    <div class="max-w-6xl mx-auto px-6">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">Tại Sao Chọn Chúng Tôi?</h2>
        <p class="text-lg text-gray-600">Công nghệ AI tiên tiến mang đến trải nghiệm test IQ tốt nhất</p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Feature 1 -->
        <div class="text-center p-6 bg-blue-50 rounded-2xl hover:bg-blue-100 transition-colors duration-300 group">
          <div class="w-16 h-16 mx-auto mb-4 bg-blue-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white">
              <circle cx="12" cy="12" r="10"/>
              <circle cx="12" cy="12" r="6"/>
              <circle cx="12" cy="12" r="2"/>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">Độ Chính Xác Cao</h3>
          <p class="text-gray-600">Thuật toán AI được kiểm chứng với độ chính xác 99.7%</p>
        </div>

        <!-- Feature 2 -->
        <div class="text-center p-6 bg-purple-50 rounded-2xl hover:bg-purple-100 transition-colors duration-300 group">
          <div class="w-16 h-16 mx-auto mb-4 bg-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white">
              <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">Kết Quả Tức Thì</h3>
          <p class="text-gray-600">Nhận kết quả và phân tích chi tiết ngay sau khi hoàn thành</p>
        </div>

        <!-- Feature 3 -->
        <div class="text-center p-6 bg-green-50 rounded-2xl hover:bg-green-100 transition-colors duration-300 group">
          <div class="w-16 h-16 mx-auto mb-4 bg-green-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white">
              <circle cx="12" cy="8" r="7"/>
              <polyline points="8.21,13.89 7,23 12,20 17,23 15.79,13.88"/>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">Chứng Chỉ Quốc Tế</h3>
          <p class="text-gray-600">Chứng chỉ IQ được công nhận và có giá trị toàn cầu</p>
        </div>
      </div>
    </div>
  </section>
</BaseLayout> 