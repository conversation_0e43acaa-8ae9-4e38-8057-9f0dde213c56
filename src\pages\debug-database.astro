---
import { supabase } from '../../backend/config/supabase';

let testResults = '';
let rawData = null;
let errorMessage = '';

try {
  console.log('🔍 Checking user_test_results table...');
  
  // Query dữ liệu từ bảng user_test_results
  const { data: results, error } = await supabase
    .from('user_test_results')
    .select(`
      id,
      user_id,
      score,
      name,
      email,
      age,
      country,
      country_code,
      gender,
      tested_at,
      duration_seconds
    `)
    .order('tested_at', { ascending: false })
    .limit(20); // Lấy 20 records mới nhất

  if (error) {
    throw error;
  }

  rawData = results || [];
  
  // Phân tích dữ liệu
  const totalRecords = rawData.length;
  const recordsWithEmail = rawData.filter(r => r.email).length;
  const recordsWithCountry = rawData.filter(r => r.country || r.country_code).length;
  const recordsWithName = rawData.filter(r => r.name).length;
  const uniqueEmails = new Set(rawData.filter(r => r.email).map(r => r.email)).size;
  const uniqueCountries = new Set(rawData.filter(r => r.country).map(r => r.country)).size;
  const scoreRange = rawData.length > 0 ? {
    min: Math.min(...rawData.map(r => r.score)),
    max: Math.max(...rawData.map(r => r.score)),
    avg: Math.round(rawData.reduce((sum, r) => sum + r.score, 0) / rawData.length)
  } : null;

  testResults = `
✅ DATABASE CHECK RESULTS (Latest 20 records):

📊 OVERVIEW:
- Total Records: ${totalRecords}
- Records with Email: ${recordsWithEmail}
- Records with Name: ${recordsWithName}
- Records with Country: ${recordsWithCountry}
- Unique Emails: ${uniqueEmails}
- Unique Countries: ${uniqueCountries}

🎯 SCORE ANALYSIS:
${scoreRange ? `- Min Score: ${scoreRange.min}
- Max Score: ${scoreRange.max}
- Average Score: ${scoreRange.avg}` : '- No score data'}

📈 RECENT TESTS:
${rawData.slice(0, 10).map((record, index) => 
  `${index + 1}. ${record.name || 'Anonymous'} - Score: ${record.score} - Country: ${record.country || record.country_code || 'N/A'} - Date: ${new Date(record.tested_at).toLocaleString('vi-VN')}`
).join('\n')}

🌍 COUNTRIES FOUND:
${[...new Set(rawData.filter(r => r.country).map(r => r.country))].slice(0, 15).join(', ')}
  `;
  
} catch (error) {
  errorMessage = error.message;
  testResults = `❌ DATABASE ERROR: ${error.message}`;
  console.error('Database check error:', error);
}
---

<!DOCTYPE html>
<html>
<head>
  <title>Debug Database - user_test_results</title>
  <meta charset="utf-8">
  <style>
    body { font-family: monospace; padding: 20px; background: #f5f5f5; }
    .container { background: white; padding: 20px; border-radius: 8px; max-width: 1200px; }
    pre { background: #f8f8f8; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    h1 { color: #333; }
    .success { color: #22c55e; }
    .error { color: #ef4444; }
    .nav { margin-bottom: 20px; }
    .nav a { margin-right: 15px; color: #0066cc; text-decoration: none; }
    .nav a:hover { text-decoration: underline; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 11px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .truncate { max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
  </style>
</head>
<body>
  <div class="container">
    <div class="nav">
      <a href="/leaderboard">← Leaderboard</a>
      <a href="/test/iq">IQ Test</a>
      <a href="#" onclick="location.reload()">🔄 Refresh</a>
    </div>
    
    <h1>🗄️ Database Check: user_test_results</h1>
    <p>Checked at: {new Date().toLocaleString('vi-VN')}</p>
    
    <h2>📊 Summary Results:</h2>
    <pre class={errorMessage ? 'error' : 'success'}>{testResults}</pre>
    
    {rawData && rawData.length > 0 && (
      <>
        <h2>📋 Recent Records Table:</h2>
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>Name</th>
              <th>Email</th>
              <th>Score</th>
              <th>Country</th>
              <th>Country Code</th>
              <th>Age</th>
              <th>Gender</th>
              <th>Duration (s)</th>
              <th>Tested At</th>
            </tr>
          </thead>
          <tbody>
            {rawData.map((record) => (
              <tr key={record.id}>
                <td class="truncate">{record.id}</td>
                <td>{record.name || '-'}</td>
                <td class="truncate">{record.email || '-'}</td>
                <td><strong>{record.score}</strong></td>
                <td>{record.country || '-'}</td>
                <td>{record.country_code || '-'}</td>
                <td>{record.age || '-'}</td>
                <td>{record.gender || '-'}</td>
                <td>{record.duration_seconds || '-'}</td>
                <td class="truncate">{new Date(record.tested_at).toLocaleString('vi-VN')}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </>
    )}
    
    <h2>🔥 Raw JSON Data (Latest 5):</h2>
    <pre>{rawData ? JSON.stringify(rawData.slice(0, 5), null, 2) : 'No data'}</pre>
  </div>
</body>
</html> 