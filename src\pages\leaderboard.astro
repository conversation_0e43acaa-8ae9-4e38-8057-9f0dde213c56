---
import BaseLayout from '../layouts/BaseLayout.astro';
import LeaderboardList from '../components/leaderboard/global/LeaderboardList.tsx';
import LeaderboardStats from '../components/leaderboard/global/LeaderboardStats.tsx';
import LocalRankingWrapper from '../components/leaderboard/local/LocalRankingWrapper.tsx';
import TopTenLeaderboard from '../components/leaderboard/global/TopTenLeaderboard.tsx';

// ✅ SUPER OPTIMIZED: Load tất cả data song song trong 1 lần
import { getLeaderboard } from '../../backend/utils/leaderboard-service';
import { getDashboardStats } from '../../backend/utils/dashboard-stats-service';

// 🚀 PARALLEL LOADING: Load tất cả data cần thiết song song
let dashboardStats;
let leaderboardData;
let topTenData;
let fullLeaderboardData;

try {
  console.log('⚡ Loading all data in parallel...');
  const startTime = Date.now();
  
  // Load song song tất cả data cần thiết
  const [statsResult, fullDataResult] = await Promise.all([
    getDashboardStats(),
    getLeaderboard(1, 600, true) // 🎯 SMART: Enable full cache for frontend pagination
  ]);

  dashboardStats = statsResult;
  leaderboardData = fullDataResult;
  
  // Tách data cho từng component
  topTenData = fullDataResult?.data?.slice(0, 10) || [];
  fullLeaderboardData = {
    data: fullDataResult?.data?.slice(10) || [], // Bỏ top 10, còn lại 590 records
    stats: fullDataResult?.stats,
    totalPages: Math.ceil((fullDataResult?.data?.length - 10 || 0) / 15) // 🔥 FIX: Tính đúng total pages
  };

  const loadTime = Date.now() - startTime;
  console.log(`✅ All data loaded in ${loadTime}ms`);
  console.log(`📊 Total records: ${fullDataResult?.data?.length || 0}`);
  console.log(`📄 Full leaderboard pages: ${fullLeaderboardData.totalPages}`);
  
} catch (error) {
  console.error('❌ Lỗi load data:', error);
  dashboardStats = null;
  leaderboardData = [];
  topTenData = [];
  fullLeaderboardData = { data: [], stats: null, totalPages: 0 };
}

const getBadgeInfo = (badge: string) => {
  switch(badge) {
    case 'genius': return { label: 'Thiên tài', color: 'bg-purple-500' };
    case 'superior': return { label: 'Xuất sắc', color: 'bg-blue-500' };
    case 'above': return { label: 'Trên TB', color: 'bg-green-500' };
    default: return { label: 'Tốt', color: 'bg-orange-500' };
  }
};

const getGenderIcon = (gender?: string) => {
  switch(gender) {
    case 'male': return '♂️';
    case 'female': return '♀️';
    case 'other': return '⚧️';
    default: return '';
  }
};
---

<BaseLayout
  title="Bảng xếp hạng IQ - Thách thức trí tuệ"
  description="Khám phá bảng xếp hạng IQ với những tài năng xuất sắc nhất. So sánh kết quả và thách thức bản thân với cộng đồng."
>
  <!-- Streamlined Hero Section -->
  <section class="relative bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-950 pt-24 pb-12 overflow-hidden">
    <div class="absolute inset-0 bg-gradient-to-br from-yellow-400/5 to-orange-500/5 dark:from-yellow-400/10 dark:to-orange-500/10"></div>
    
    <div class="relative max-w-6xl mx-auto px-4 text-center">
      <div class="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      </div>
      
      <h1 class="text-3xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-4">
        Bảng xếp hạng <span class="text-yellow-600 dark:text-yellow-500">IQ</span>
      </h1>
      
      <p class="text-lg text-gray-600 dark:text-gray-300 mb-8">Khám phá những tài năng xuất sắc nhất</p>

      <!-- ✅ INSTANT DISPLAY: Có initialStats từ server -->
      <LeaderboardStats client:load initialStats={dashboardStats} />
    </div>
  </section>

  <!-- Top 10 Leaderboard Section -->
  <section class="bg-white dark:bg-gray-900 py-8">
    <div class="max-w-6xl mx-auto px-4">
      <TopTenLeaderboard client:load initialData={topTenData} />
    </div>
  </section>

  <!-- Local Ranking Section for Logged-in Users -->
  <section class="py-8 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800">
    <div class="max-w-6xl mx-auto px-4">
      <LocalRankingWrapper client:load />
    </div>
  </section>

  <!-- Optimized Full Leaderboard -->
  <section id="full-leaderboard" class="py-8 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
    <div class="max-w-6xl mx-auto px-4">
      <!-- ✅ OPTIMIZED: Pass pre-processed data -->
      <LeaderboardList client:load initialData={fullLeaderboardData} />
    </div>
  </section>
</BaseLayout>

 